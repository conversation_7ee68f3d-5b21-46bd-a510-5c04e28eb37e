/* @import "responsive.css"; */
@import "animate.css";
@import "home.css";
@import "skills.css";
@import "project.css";
@import "about.css";
@import "contact.css";

:root {
  --color-primary: #5462ffe4;
  --color-primary-rgb: 84, 98, 255;
  --color-secondary: #fff3f1;
  --color-secondary-dark: #808793;
  --color-light: #fff;
  --transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  --radius: 0.75rem;
  --color-secondary-text: #808793;

  --bg1: linear-gradient(135deg, #f8f9ff 0%, #e8ecff 100%);
  --bg2: linear-gradient(135deg, #f0f2f5 0%, #dde1e7 100%);
  --text-color: #374151;
  --overlay-bg: linear-gradient(
    135deg,
    rgba(0, 0, 0, 0.85) 0%,
    rgba(84, 98, 255, 0.9) 100%
  );
  --overlay-text: #fff;

  /* Enhanced shadow system */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-primary: 0 12px 40px rgba(var(--color-primary-rgb), 0.15);
}

*,
*::before,
*::after {
  margin: 0;
  padding: 0;
  box-sizing: inherit;
}

html {
  box-sizing: border-box;
}

body {
  line-height: 1.4;
  color: var(--color-secondary);
  font-family: "Raleway", sans-serif;
  background-image: radial-gradient(
    circle,
    #051937,
    #0e142c,
    #100f21,
    #0d0817,
    #05020a
  );
}
.brand {
  font-size: 1.3rem;
  font-weight: 900;
  color: var(--color-light);
}
.center {
  width: 90%;
  margin: 0 auto;
  display: flex;
  justify-content: center;
  align-items: center;
}

#content {
  max-width: 1200px;
  width: 90%;
  margin: 0 auto;
}

img {
  max-width: 100%;
  height: auto;
}

a {
  text-decoration: none;
  color: inherit;
}

/* header section */
.header {
  display: flex;
  justify-content: space-between;
  height: 50px;
  align-items: center;
  padding: 0 15px;

  width: 90%;
  margin: 0 auto;
  position: sticky;
  top: 1rem;
  border: 1px solid rgba(255, 255, 255, 0.3);
  z-index: 99;
  background: rgba(29, 29, 29, 0.8);
  backdrop-filter: blur(10px);
  border-radius: var(--radius);
}

.burger-menu {
  display: none;
  font-size: 20px;
  cursor: pointer;
  background: transparent;
  border: none;
  color: var(--color-light);
  padding: 5px;
  line-height: 0;
}

.nav__menu {
  display: flex;
  margin-bottom: 0.5rem;
}

.nav__btn {
  color: #fff;
  font-weight: 700;
}

.nav__link {
  color: var(--color-light);
  background-color: transparent;
  padding: 10px 20px;
  border-radius: 5px;
  font-size: 0.8rem;
  font-weight: 600;
  transition: var(--transition);
  border: 1px solid var(--color-primary);
  position: relative;
  overflow: hidden;
  transform: translateY(0);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.nav__link::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    var(--color-primary),
    var(--color-primary-variant, #6366f1)
  );
  transform: translateY(100%);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: -1;
  border-radius: inherit;
}

.nav__link::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.6s ease, height 0.6s ease;
}

.nav__link:hover {
  color: var(--color-white, #ffffff);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: transparent;
}

.nav__link:hover::before {
  transform: translateY(0);
  color: #e2e2e2;
}

.nav__link:hover::after {
  width: 300px;
  height: 300px;
}

.nav__link:active {
  transform: translateY(-1px);
  transition: transform 0.1s ease;
}

.nav__link:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
  transform: translateY(-1px);
  border-color: transparent;
}

/* Active state styles */
.nav__btn.active {
  color: var(--color-primary);
  font-weight: bold;
}

.nav__link.active {
  border-color: var(--color-primary);
}

.hero {
  min-height: 80vh;
  display: flex;
  overflow: hidden;
}

.hero__content {
  display: flex;
  align-items: flex-start;
  flex-direction: column;
  margin-top: 4rem;
  width: 50%;
}

.main__title {
  font-family: "Mitr", sans-serif;
  font-size: 2rem;
  padding-right: 3rem;
  font-weight: 400;
  color: var(--color-light);
  line-height: 40px;
  text-transform: capitalize;

  display: flex;
  flex-direction: column;
}

.main__title-text {
  opacity: 0;
  transform: translate3d(0, -2px, 0);
  animation-name: fadeIn;
  animation-timing-function: ease-in-out;
  animation-fill-mode: forwards;
}

.sub__title {
  font-size: 0.9rem;
  color: var(--color-secondary-text);
  font-weight: 300;
  width: 70%;
  margin: 1.2rem 0;
  transform: translateY(10px);
  animation: slideTextDown 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
}

.btn {
  font-size: 1.125rem;
  font-family: "Mitr", sans-serif;
  border: 0;
  text-transform: capitalize;
  cursor: pointer;
}

.btn--hero {
  color: var(--color-light);
  background: linear-gradient(135deg, var(--color-primary), #7c3aed);
  border-radius: 7px;
  animation: slideUp 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.5s both;
  border: 1px solid var(--color-primary);
  font-size: 0.9rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 8px 25px rgba(84, 98, 255, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.1);
  padding: 0.8rem 2rem;
}

.btn--hero:hover {
  background-color: var(--color-primary);
  backdrop-filter: blur(10px);
  border-color: var(--color-primary);
  box-shadow: 0 0 10px rgba(255, 255, 225, 0.6);
}

.nav__btn {
  margin-right: 1.2rem;
  background-color: transparent;
  border: 2px solid transparent;
  transition: var(--transition);
  font-size: 0.9rem;
  font-weight: 400;
}

.nav__btn:hover {
  color: var(--color-primary);
  border-bottom-color: var(--color-primary);
}

.hero__img {
  animation: slideInRight 1.2s cubic-bezier(0.16, 1, 0.3, 1) 0.3s both;
  width: 50%;
}
.about__hero-content {
  flex: 1;
}

.hero-flex {
  flex: 1;
}

.alert__badge {
  position: fixed;
  bottom: 5%;
  right: 2%;
  background-color: var(--color-secondary-text);
  min-width: 150px;
  min-height: 30px;
  border-radius: var(--radius);
  padding: 10px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.text__box {
  position: absolute;
  bottom: 5%;
  left: 5%;
  width: 60%;
  height: 8rem;
  background-color: var(--color-secondary-text);
  border-radius: 5px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 1rem;
  z-index: 11;
  animation: slideInLeft 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.5s both;
}

.footer {
  width: 100%; /* Take full width */
  padding: 20px; /* Add padding */
  display: flex;
  justify-content: center;
}

.footer > p span {
  font-family: "Mitr", sans-serif;
  font-weight: 400;
}

.mobile__menu {
  display: none;
}

.mobile__menu {
  transition: transform 0.3s ease-in-out;
  transform: translateX(-100%);
}

.mobile__menu.open {
  transform: translateX(0);
}

.menu-open {
  overflow: hidden;
}

.menu-open::after {
  content: "";
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 98;
}

/* Small devices  */
@media (max-width: 400px) {
  .hero {
    overflow-y: scroll;
  }
  .mobile__menu .nav__btn {
    font-size: 0.8rem;
  }

  .parent__container:hover canvas {
    transform: scale(1.4);
  }

  .main__title {
    font-size: 1.5rem;
    line-height: 25px;
    text-align: left;
  }

  .sub__title {
    width: 100%;
  }

  .nav__link {
    display: none;
  }
  .burger-menu {
    display: block;
  }

  #home .hero__background-shape {
    width: 300px;
    height: 300px;
  }

  #home .image__wrapper {
    width: 200px;
    height: 200px;
    padding: 3px;
  }

  #home .image__wrapper img {
    border-width: 2px;
  }

  #home .animate-profile {
    animation: floatAnimation 3s ease-in-out infinite;
  }

  /* Project section styles handled in project.css */

  .icon__container .card__icon {
    width: 50px;
    height: auto;
  }
  .stack__card {
    gap: 0.6rem;
    margin: 0.7rem 0;
  }

  .card__container {
    padding: 5px;
    width: 8rem;
    height: 8rem;
  }

  #contact .hero__img {
    width: 100%;
  }

  .stack__card {
    position: relative;
    top: 0;
    left: 0;
    transform: translate(0, 0);
    margin-top: 2rem;
  }
}

@media (max-width: 576px) {
  .brand {
    font-size: 0.8rem;
  }

  .nav__link {
    display: none;
  }
  .burger-menu {
    display: block;
  }

  .hero {
    margin: auto 0;
  }

  .hero__content,
  .hero__img {
    width: 100%;
  }

  .parent__container {
    width: 100%;
    height: 100%;
  }

  canvas {
    width: 100% !important;
    height: 100% !important;
    object-fit: cover;
    border-radius: 15px;
    transition: transform 0.3s;
  }
  /* Project section styles handled in project.css */

  #contact .hero__img {
    width: 100%;
  }

  .hero {
    overflow-y: visible;
  }

  .footer {
    text-wrap: wrap;
    text-align: center;
  }
}

/* mobile devices */
@media (max-width: 767px) {
  .hero {
    flex-direction: column;
  }

  .hero__content {
    margin: 4rem 0 1rem 0;
  }

  .hero,
  .hero__content,
  .sub__title,
  .hero__img,
  #contact .hero__img {
    width: 100%;
  }

  #home .hero__background-shape {
    width: 380px;
    height: 380px;
  }

  #home .image__wrapper {
    width: 240px;
    height: 240px;
    padding: 4px;
  }

  #home .image__wrapper img {
    border-width: 3px;
  }

  .hero-flex {
    flex: 0;
  }

  .parent__container {
    height: 450px;
    width: 100%;
  }

  /* Skills section mobile adjustments */
  #skills .parent__container {
    height: 400px;
    width: 95%;
    margin: 0 auto;
  }

  .nav__link,
  .nav__menu {
    display: none;
  }
  .burger-menu {
    display: block;
  }

  .mobile__menu.open {
    display: flex;
    transform: translateY(0);
    opacity: 1;
  }

  .mobile__menu .nav__btn {
    font-size: 0.6rem;
    margin-right: 0.5rem;
  }

  .sub__title {
    width: 100%;
  }

  .project-card {
    width: 100%;
  }

  .modal-content {
    width: 90%;
    height: auto;
  }
  .nav__link,
  .nav__menu {
    display: none;
  }
  .burger-menu {
    display: block;
  }

  .mobile__menu {
    display: flex;
  }

  .mobile__menu {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: rgb(42, 41, 41);
    padding: 10px 20px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    justify-content: space-between;
    border-radius: var(--radius);
    margin-top: 5px;
    transform: translateY(-10px);
    opacity: 0;
    transition: var(--transition);
  }

  .mobile__menu.open {
    display: flex;
    transform: translateY(0);
    opacity: 1;
  }
}

/* Large devices (desktops, 992px and up) */
@media (min-width: 992px) {
  .parent__container {
    width: 80%;
    height: 80%;
  }

  .form__container {
    max-width: 100%;
  }
}

/* Extra large devices (large desktops, 1200px and up) */
@media (min-width: 1200px) {
  .parent__container {
    width: 100%;
    height: 100%;
  }
}

@keyframes spin {
  to {
    transform: rotate(1turn);
  }
}

/* *****************Landscape and Tablet************* */

/* Small tablets (iPad Mini, etc.) */
@media (min-width: 768px) and (max-width: 820px) {
  body {
    font-size: 17px;
  }

  .hero {
    flex-direction: column;
    min-height: 80vh;
  }

  .hero,
  .hero__content,
  .sub__title,
  .hero__img {
    width: 100%;
  }

  /* Contact section specific small tablet layout */
  #contact .hero {
    flex-direction: column;
    align-items: center;
    gap: 2rem;
  }

  #contact .hero__content {
    width: 100%;
  }

  #contact .hero__img {
    width: 100%;
  }

  .hero__content {
    margin-top: 1.5rem;
    margin-bottom: 1rem;
  }

  .hero-flex {
    flex: 0;
  }

  .parent__container {
    width: 95%;
    height: 55vh;
    max-height: 450px;
    margin: 0 auto;
  }
}

/* Large tablets and small desktops */
@media (min-width: 821px) and (max-width: 1024px) {
  body {
    font-size: 18px;
  }

  .hero {
    flex-direction: column;
    min-height: 85vh;
  }

  .hero,
  .hero__content,
  .sub__title,
  .hero__img {
    width: 100%;
  }

  /* Contact section specific tablet layout */
  #contact .hero {
    flex-direction: column;
    align-items: center;
    gap: 2rem;
  }

  #contact .hero__content {
    width: 100%;
    margin-top: 0;
    text-align: center;
  }

  #contact .hero__img {
    width: 100%;
  }

  .hero__content {
    margin-top: 2rem;
    margin-bottom: 1rem;
  }

  .hero-flex {
    flex: 0;
  }

  .parent__container {
    width: 90%;
    height: 60vh;
    max-height: 500px;
    margin: 0 auto;
  }

  .stack__card {
    justify-content: space-between;
  }

  .card__container {
    width: 13rem;
    height: 13rem;
  }

  .form__container {
    max-width: 100%;
  }

  /* Skills section specific tablet adjustments */
  #skills .hero {
    min-height: 90vh;
  }

  #skills .hero__content {
    margin-bottom: 2rem;
  }

  #skills .parent__container {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  /* Ensure canvas scales properly on tablets */
  #skills canvas {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
  }

  /* Navigation improvements for tablets */
  .nav__link {
    padding: 8px 16px;
    font-size: 0.75rem;
    margin-right: 0.8rem;
  }

  .header {
    padding: 0 20px;
  }

  /* Content wrapper improvements */
  #content {
    width: 95%;
  }
}

/* Landscape */
/* @media (max-width: 768px) and (orientation: landscape) {
  body {
    font-size: 20px;
    border: 1px solid #544c28;
  }
  .container {
    max-width: 100%;
  }
} */

@media (max-height: 500px) and (orientation: landscape) {
  body {
    font-size: 12px;
  }
  .hero {
    flex-direction: column;
    overflow: scroll;
  }

  .hero__content,
  .hero__img {
    width: 100%;
  }

  .parent__container {
    width: 100%;
    height: 400px;
  }

  .card__container {
    width: 13rem;
    height: 12rem;
  }

  .stack__card {
    position: relative;
    top: 0;
    left: 0;
    transform: translate(0, 0);
    margin-top: 2rem;
  }

  /* Project section styles handled in project.css */
}

/* ******************************** */

/*  */
@media screen and (max-width: 1024px) {
  #home .hero__background-shape {
    width: 450px;
    height: 450px;
    border: 1px solid white;
  }

  #home .image__wrapper {
    width: 280px;
    height: 280px;
  }
}

@media screen and (max-width: 480px) {
  #home .hero__background-shape {
    width: 300px;
    height: 300px;
  }

  #home .image__wrapper {
    width: 200px;
    height: 200px;
    padding: 3px;
  }

  #home .image__wrapper img {
    border-width: 2px;
  }

  #home .animate-profile {
    animation: floatAnimation 3s ease-in-out infinite;
  }
}

/* Cookie Consent Styles */
.cookie-consent {
  position: fixed;
  bottom: 20px;
  right: 20px;
  max-width: 400px;
  background: linear-gradient(
    135deg,
    var(--color-primary),
    var(--color-secondary-text)
  );
  border: 1px solid rgba(84, 98, 255, 0.2);
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15),
    0 0 0 1px rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  z-index: 1000;
  font-family: "Mitr", sans-serif;
}

.cookie-message {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 16px;
}

.cookie-icon {
  font-size: 24px;
  flex-shrink: 0;
}

.cookie-message p {
  margin: 0;
  font-size: 14px;
  line-height: 1.5;
  color: var(--color-dark);
}

.cookie-buttons {
  display: flex;
  gap: 10px;
  align-items: center;
  flex-wrap: wrap;
}

.cookie-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-family: "Mitr", sans-serif;
}

.cookie-accept {
  background: linear-gradient(135deg, var(--color-primary), #7c3aed);
  color: white;
  box-shadow: 0 4px 12px rgba(84, 98, 255, 0.3);
  border: 2px solid var(--color-primary);
}

.cookie-accept:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(84, 98, 255, 0.4);
}

.cookie-decline {
  background: rgba(var(--color-primary), 0.15);
  color: var(--color-secondary);
  border: 1px solid rgba(107, 114, 128, 0.2);
}

.cookie-decline:hover {
  background: rgba(var(--color-primary), 0.25);
  transform: translateY(-1px);
}

.cookie-learn-more {
  font-size: 12px;
  color: var(--color-primary);
  text-decoration: none;
  margin-left: auto;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.cookie-learn-more:hover {
  background: rgba(84, 98, 255, 0.1);
  text-decoration: underline;
}

/* Responsive adjustments */
@media (max-width: 480px) {
  .cookie-consent {
    bottom: 10px;
    right: 10px;
    left: 10px;
    max-width: none;
  }

  .cookie-buttons {
    justify-content: space-between;
  }

  .cookie-btn {
    flex: 1;
    min-width: 80px;
  }
}

/* Duplicate cookie styles removed - keeping only the original ones above */
