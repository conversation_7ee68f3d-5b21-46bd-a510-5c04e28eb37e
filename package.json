{"name": "portfolio", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"build": "webpack --mode production", "dev": "webpack serve --open", "watch": "webpack --watch"}, "keywords": [], "author": "Jayblacc", "license": "ISC", "devDependencies": {"@babel/core": "^7.23.9", "@babel/preset-env": "^7.23.9", "babel-loader": "^9.1.3", "copy-webpack-plugin": "^12.0.2", "css-loader": "^6.10.0", "file-loader": "^6.2.0", "html-webpack-plugin": "^5.6.3", "image-minimizer-webpack-plugin": "^4.1.3", "mini-css-extract-plugin": "^2.8.0", "style-loader": "^3.3.4", "url-loader": "^4.1.1", "webpack-cli": "^5.1.4", "webpack-dev-server": "^5.0.3"}, "dependencies": {"aos": "^2.3.4", "particles.js": "^2.0.0"}}